# Automoment Mobile Application - Technical Documentation

## Executive Summary

Automoment is a Flutter-based mobile application that leverages the GetX state management pattern for efficient and reactive state management. The application follows a GetX-based architecture with clear separation of view and logic, featuring a well-organized project structure, centralized API handling, and integration with multiple third-party services including Firebase. This document provides a technical overview of the application's architecture, key components, and implementation details.

## Key Features

### User Management
- Multi-method authentication (Email/Password, Google, Apple)
- User profile management
- Account settings and preferences

### Event Management
- Event discovery and browsing
- Event registration and booking
- Event form completion and signing
- Participant management

### Automotive Features
- Car listings and details
- Booking management
- Check-in functionality
- Add-on services

### Social and Community
- Chat functionality (using Firebase Realtime Database)
- Leaderboard and rankings- News feed and updates
- Notifications system

### E-commerce
- Shopping cart functionality
- Product catalog
- Secure payment processing
- Order tracking and history

### Rewards and Loyalty
- Reward points system
- Lifestyle content
- Rebate claims
- Wallet/credit balance

## Technology Stack

### Core Technologies
- **Flutter SDK**: v3.29.2 (stable channel) - Cross-platform UI toolkit
- **Dart SDK**: v3.7.2 - Programming language
- **GetX**: v4.6.6 - State management, routing, and dependency injection
- **Firebase Suite**: 
  - Core: v3.4.0
  - Auth: v5.5.3
  - Messaging: v15.1.0
  - Analytics: v11.3.0
  - Database: v11.1.0
  - Storage: v12.2.0

### Key Dependencies
- **HTTP Client**: http (v1.2.2) with pretty_http_logger for API communication
- **Authentication**: Firebase Auth (v5.5.3) with Google and Apple Sign-In
- **Storage**: Firebase Storage (v12.2.0) and GetStorage for local persistence
- **Messaging**: Firebase Messaging (v15.1.0) for push notifications
- **UI Components**: Various UI libraries including flutter_chat_ui, carousel_slider, and card_swiper
- **Deep Linking**: app_links (v6.4.0) for handling deep links
- **Analytics**: Firebase Analytics (v11.3.0) and Sentry for error tracking

## Architecture Overview

### GetX-Based Architecture Implementation

The application follows a GetX-based architecture pattern with clear separation of view and logic:

1. **Views Layer**: UI components and screens in the modules/*/views directories
2. **Controllers Layer**: Business logic in the modules/*/controllers directories
3. **Bindings Layer**: Dependency injection in the modules/*/bindings directories
4. **Services Layer**: API clients and external integrations in the services directory
5. **Models Layer**: Data models for the application

This architecture leverages GetX's state management capabilities to maintain a clear separation between UI and business logic while avoiding the complexity of a full clean architecture implementation.

### Project Structure

```
lib/
├── app/
│   ├── constants/       # App-wide constants and configuration
│   ├── controllers/     # GetX controllers for state management
│   ├── data/           
│   │   └── models/      # Data models
│   ├── helpers/         # Utility functions
│   ├── modules/         # Feature modules (screens and their controllers)
│   ├── routes/          # App routing configuration
│   └── services/        # Service layer for API and external integrations
│       └── api_client.dart
├── main.dart            # Application entry point
```

### State Management

The application uses GetX for state management, which provides:
- Reactive state management with observable variables
- Dependency injection for controllers and services
- Navigation and routing system
- Lifecycle management

## Key Components

### API Client

The API client (`api_client.dart`) is a centralized service for all backend communication:

- Implements RESTful API calls using HTTP
- Returns raw string responses as per the established pattern
- Handles authentication tokens and headers
- Implements error handling and response validation
- Logging capabilities for debugging (configurable)

### Authentication System

The application implements a hybrid authentication system:

1. **Email/Password Authentication**: Traditional login flow with email verification handled by the app's own user management system

2. **Social Authentication**: 
   - **Google Sign-In**: Implemented using Firebase Authentication
   - **Apple Sign-In**: Implemented using Firebase Authentication with secure nonce generation

The `FirebaseAuthService` class handles the social authentication flows, while the application's own user management system is used for email authentication. After successful social authentication, the Firebase ID token is sent to the backend API to integrate with the app's user management system, creating a unified authentication experience.

### Deep Linking System

The application implements a comprehensive deep linking system (`DeepLinkService`) that:

- Handles both cold starts and app-running scenarios
- Supports multiple link types (events, news, lifestyle, leaderboard, etc.)
- Uses a standardized JSON format for all deep link types: `{"id": id}`
- Manages navigation based on link type and parameters
- Preserves deep link state across app restarts

### Payment Integration

The application integrates with Stripe for payment processing, supporting multiple payment methods:

1. **Credit/Debit Card Payments**:
   - Secure integration with Stripe SDK (v11.0.0)
   - Client-side payment intent creation
   - Support for different currencies (SGD, MYR)
   - Coupon code application for discounts

2. **PayNow Integration**:
   - Singapore's local payment method integration
   - QR code generation and display via WebView
   - Payment status verification with polling mechanism
   - Automatic saving of QR codes to gallery on Android

3. **Wallet/Credit Balance**:
   - In-app wallet balance management and display
   - Credit balance payment option for purchases
   - Real-time balance updates after transactions
   - Wallet top-up handled in backend through manual payment processing

The payment system is modular and handles different registration types (driver, passenger, additional driver, group) with appropriate pricing logic for each type.

### Push Notification System

The application implements Firebase Cloud Messaging for push notifications with:

- Foreground and background message handling
- Local notification display using Flutter Local Notifications
- Custom notification channels for Android
- Deep link integration for notification actions
- Notification permission handling for iOS

### Module-Based Architecture

The application is organized into feature modules, each containing:
- Views (UI components)
- Controllers (business logic)
- Bindings (dependency injection)
- Routes (navigation)

This modular approach allows for better code organization, reusability, and maintainability.

## Security Considerations

1. **Token Management**: Secure token handling using GetStorage for persistent token storage with appropriate token lifecycle management (storage on login, removal on logout)

2. **API Security**: Implementation of secure API communication with proper headers (content-type, authorization) and HTTPS for all network requests

3. **Social Authentication**: Secure implementation of Google and Apple Sign-In with appropriate error handling

4. **Anti-Replay Protection**: Implementation of cryptographically secure nonce generation and SHA-256 hashing for Apple Sign-In to prevent replay attacks

5. **Error Monitoring**: Integration with Sentry for real-time error tracking, crash reporting, and security issue monitoring


## Current Application Focus

The application currently prioritizes feature development and functionality over specific performance optimizations. The architecture leverages some inherent benefits from the technology choices:

1. **GetX Framework**: Utilizes GetX's efficient state management which can help with UI rendering performance

2. **Modular Structure**: The module-based architecture helps maintain code organization as the application grows

3. **On-Demand Loading**: Controllers and dependencies are initialized when needed through GetX bindings

Future performance optimization opportunities may include implementing more aggressive caching strategies, optimizing image loading, and implementing performance monitoring.

## Conclusion

The Automoment mobile application is built on a solid technical foundation with modern architecture patterns and technologies. The GetX-based architecture with clear separation of view and logic provides a maintainable and scalable codebase. The standardized approach to API communication and deep linking ensures consistency across the application.

The integration with Firebase services provides robust authentication, push notifications, and analytics, with Firebase Realtime Database used specifically for the chat functionality. This comprehensive suite of features makes the application feature-rich and user-friendly.

---

*Document prepared: May 16, 2025*
