import 'dart:io';

import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:automoment/app/controllers/nav_history_controller.dart';
import 'package:flutter/material.dart';
import 'package:email_validator/email_validator.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';

import '../../../routes/app_pages.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/login_controller.dart';

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  final LoginController controller = Get.put(LoginController());

  final NavHistoryController navHistoryController =
      Get.put(NavHistoryController());

  @override
  void dispose() {
    Get.delete<LoginController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        top: false,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          reverse: false,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                    offset: Offset(0, -5),
                    blurRadius: 15,
                    color: Colors.black12),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                getHeader(),
                buildLoginForm(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding getHeader() {
    return Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            Row(
              children: [
                const Spacer(),
                IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: const Icon(Icons.close),
                  iconSize: 30,
                  color: Colors.black,
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Welcome Back!",
                    style: AppTextStyles.bigHeaderText,
                  )),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Login now and check out the latest deals available!",
                    style: AppTextStyles.normalText,
                  )),
            ),
          ],
        ));
  }

  Padding buildLoginForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
          left: AppConfig.defaultPadding * 2,
          right: AppConfig.defaultPadding * 2,
          bottom: AppConfig.defaultPadding * 2),
      child: Column(
        children: [
          Form(
            key: controller.formKey.value,
            child: Column(
              children: [
                TextFormField(
                  controller: controller.emailController,
                  keyboardType: TextInputType.emailAddress,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Email",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }

                    if (!EmailValidator.validate(value)) {
                      return "Invalid email pattern";
                    }
                    return null;
                  },
                ),
                Obx(() => TextFormField(
                      controller: controller.passwordController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      obscureText: controller.isPasswordHidden.value,
                      decoration: InputDecoration(
                        labelText: "Password",
                        hintText: "",
                        suffixIcon: IconButton(
                          icon: Icon(
                            controller.isPasswordHidden.value
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            controller.isPasswordHidden.value =
                                !controller.isPasswordHidden.value;
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    )),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: AppConfig.defaultPadding * 2),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        child: const Text("Forgot password?"),
                        onTap: () {
                          Get.toNamed(Routes.FORGOT_PASSWORD_EMAIL);
                        },
                      ),
                    ],
                  ),
                ),
                //remeber me checkbox
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Row(children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: Obx(() => Checkbox(
                            activeColor: AppColor.secondaryColor,
                            value: controller.isChecked.value,
                            onChanged: (value) {
                              controller.isChecked.value =
                                  !controller.isChecked.value;
                            },
                          )),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    const Flexible(
                      child: Column(children: [
                        Text("Remember me"),
                      ]),
                    ),
                  ]),
                ),
                const SizedBox(
                  height: 30,
                ),
                ElevatedButton(
                    style: ButtonStyle(
                        foregroundColor:
                            WidgetStateProperty.all<Color>(Colors.white),
                        backgroundColor: WidgetStateProperty.all<Color>(
                            AppColor.primaryButtonColor),
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side: const BorderSide(
                                    color: AppColor.primaryButtonColor)))),
                    onPressed: () async {
                      if (controller.formKey.value.currentState!.validate()) {
                        if (await controller.login()) {
                          if (navHistoryController.backPageArgs == null) {
                            Get.offAllNamed(Routes.BOTTOMBAR);
                          } else {
                            //TODO: maybe need to fix it
                            Get.offAllNamed(Routes.BOTTOMBAR);
                            Get.toNamed(navHistoryController.backPage,
                                arguments: navHistoryController.backPageArgs);
                          }

                          debugPrint(
                              "login success, token: ${controller.token}");
                        } else {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return KMessageDialogView(
                                    content: controller.message);
                              });
                          debugPrint("login failed ${controller.message}");
                        }
                      } else {
                        debugPrint("not ok");
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: const Center(
                        child: Text("Sign In", style: TextStyle(fontSize: 16)),
                      ),
                    )),
                const SizedBox(
                  height: 10,
                ),
                ElevatedButton(
                    style: ButtonStyle(
                        foregroundColor:
                            WidgetStateProperty.all<Color>(Colors.white),
                        backgroundColor: WidgetStateProperty.all<Color>(
                            AppColor.primaryButtonColor),
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side: const BorderSide(
                                    color: AppColor.primaryButtonColor)))),
                    onPressed: () async {
                      if (await controller.signInWithGoogle()) {
                        if (controller.isAllRequiredUserFieldsFilled()) {
                          if (navHistoryController.backPageArgs == null) {
                            Get.offAllNamed(Routes.BOTTOMBAR);
                          } else {
                            Get.offAllNamed(Routes.BOTTOMBAR);
                            Get.toNamed(navHistoryController.backPage,
                                arguments: navHistoryController.backPageArgs);
                          }
                        } else {
                          Get.offAllNamed(Routes.SOCIAL_LOGIN_FORM,
                              arguments: "Google");
                        }
                      } else {
                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return KMessageDialogView(
                                  content: controller.message);
                            });
                        debugPrint("login failed ${controller.message}");
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: Row(
                        children: [
                          const Spacer(),
                          Image.asset(
                            "assets/images/google.png",
                            height: 20,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          const Text("Sign In with Google",
                              style: TextStyle(fontSize: 16)),
                          const Spacer(),
                        ],
                      ),
                    )),
                const SizedBox(
                  height: 10,
                ),
                (Platform.isIOS)
                    ? ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0),
                                    side: const BorderSide(
                                        color: AppColor.primaryButtonColor)))),
                        onPressed: () async {
                          //controller.signInWithGoogle();
                          if (await controller.signInWithApple()) {
                            if (controller.isAllRequiredUserFieldsFilled()) {
                              if (navHistoryController.backPageArgs == null) {
                                Get.offAllNamed(Routes.BOTTOMBAR);
                              } else {
                                Get.offAllNamed(Routes.BOTTOMBAR);
                                Get.toNamed(navHistoryController.backPage,
                                    arguments:
                                        navHistoryController.backPageArgs);
                              }
                            } else {
                              Get.offAllNamed(Routes.SOCIAL_LOGIN_FORM,
                                  arguments: "Apple");
                            }
                          } else {
                            showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return KMessageDialogView(
                                      content: controller.message);
                                });
                            debugPrint("login failed ${controller.message}");
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: Row(
                            children: [
                              const Spacer(),
                              Image.asset(
                                "assets/images/apple.png",
                                height: 20,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Text("Sign In with Apple",
                                  style: TextStyle(fontSize: 16)),
                              const Spacer(),
                            ],
                          ),
                        ))
                    : Container(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
