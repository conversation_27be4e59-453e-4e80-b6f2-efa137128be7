import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/helpers/notification_util.dart';

import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/notification_model.dart';
import '../../../services/api_client.dart';

//TODO: back button need to check navigation stack, if 0 load bottom bar

class NotificationDetailController extends BaseScreenController {
  @override
  String get screenName => 'Notification Detail';

  var notification = Notification().obs;

  final UserController userController = Get.put(UserController());

  var urls = [].obs;

  @override
  Future<void> onInit() async {
    super.onInit();

    if (Get.arguments is Notification) {
      notification.value = Get.arguments;
    } else {
      await fetchNotificationDetail();
    }

    if (userController.isLoggedIn.value) {
      NotificationUtil.callMarkAsRead(userController.getToken(),
          userController.getUser().id!, notification.value.id!);
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> fetchNotificationDetail() async {
    EasyLoading.show(status: 'loading...');
    try {
      int id = int.parse(Get.arguments);
      var response =
          await ApiClient.getNotificationDetail(userController.getToken(), id);
      notification.value =
          Notification.fromJson(jsonDecode(response)['notification']);
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
    }
  }
}
