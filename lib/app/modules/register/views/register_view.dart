import 'dart:io';

import 'package:country_picker/country_picker.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/register_controller.dart';

class RegisterView extends StatefulWidget {
  const RegisterView({super.key});

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> {
  final RegisterController controller = Get.put(RegisterController());

  @override
  void dispose() {
    Get.delete<RegisterController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        top: false,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          reverse: false,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                    offset: Offset(0, -5),
                    blurRadius: 15,
                    color: Colors.black12),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                getHeader(),
                buildRegisterForm(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding getHeader() {
    return Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            Row(
              children: [
                const Spacer(),
                IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: const Icon(Icons.close),
                  iconSize: 30,
                  color: Colors.black,
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Create an account",
                    style: AppTextStyles.bigHeaderText,
                  )),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Sign up to get started!",
                    style: AppTextStyles.normalText,
                  )),
            ),
          ],
        ));
  }

  Padding buildRegisterForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
          left: AppConfig.defaultPadding * 2,
          right: AppConfig.defaultPadding * 2,
          bottom: AppConfig.defaultPadding * 2),
      child: Column(
        children: [
          Form(
            key: controller.formKey.value,
            child: Column(
              children: [
                TextFormField(
                  controller: controller.nameController,
                  keyboardType: TextInputType.text,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Full Name",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: controller.emailController,
                  keyboardType: TextInputType.emailAddress,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Email",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }

                    if (!EmailValidator.validate(value)) {
                      return "Invalid email pattern";
                    }
                    return null;
                  },
                ),
                Obx(
                  () => TextFormField(
                    controller: controller.passwordController,
                    keyboardType: TextInputType.text,
                    autocorrect: false,
                    obscureText: controller.isPasswordHidden.value,
                    decoration: InputDecoration(
                      labelText: "Password",
                      hintText: "",
                      suffixIcon: IconButton(
                        icon: Icon(
                          controller.isPasswordHidden.value
                              ? Icons.visibility_off
                              : Icons.visibility,
                        ),
                        onPressed: () {
                          controller.isPasswordHidden.value =
                              !controller.isPasswordHidden.value;
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value!.isEmpty) {
                        return 'Required';
                      }
                      return null;
                    },
                  ),
                ),
                Obx(() => TextFormField(
                      controller: controller.passwordConfirmController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      obscureText: controller.isConfirmPasswordHidden.value,
                      decoration: InputDecoration(
                        labelText: "Confirm Password",
                        hintText: "",
                        suffixIcon: IconButton(
                          icon: Icon(
                            controller.isConfirmPasswordHidden.value
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            controller.isConfirmPasswordHidden.value =
                                !controller.isConfirmPasswordHidden.value;
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    )),
                TextFormField(
                  controller: controller.addressController,
                  keyboardType: TextInputType.text,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Address",
                    hintText: "",
                  ),
                  validator: (value) {
                    return null;

                    // if (value!.isEmpty) {
                    //   return 'Required';
                    // }
                  },
                ),

                GestureDetector(
                  onTap: () {
                    showCountryPicker(
                      context: context,
                      showPhoneCode: false,
                      favorite: ['SG', 'MY'],
                      onSelect: (Country country) {
                        controller.countryController.value.text = country.name;
                      },
                    );
                  },
                  child: AbsorbPointer(
                    child: TextFormField(
                      controller: controller.countryController.value,
                      keyboardType: TextInputType.text,
                      //enabled: false,
                      readOnly: true,
                      decoration: const InputDecoration(
                        labelText: "Country",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    ),
                  ),
                ),

                InternationalPhoneNumberInput(
                  hintText: "Mobile Number",
                  onInputChanged: (PhoneNumber number) {
                    debugPrint("mobile: ${number.phoneNumber}");
                  },
                  onInputValidated: (bool value) {
                    debugPrint("valid: $value");
                  },
                  selectorConfig: const SelectorConfig(
                    selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                  ),
                  ignoreBlank: false,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                  autoValidateMode: AutovalidateMode.disabled,
                  // selectorTextStyle: TextStyle(color: Colors.black),
                  initialValue:
                      controller.mobileNumber, //PhoneNumber(isoCode: 'SG'),
                  textFieldController: controller.mobileNumberController,
                  formatInput: false,
                  keyboardType: const TextInputType.numberWithOptions(
                      signed: true, decimal: true),
                  // inputBorder: OutlineInputBorder(),
                  onSaved: (PhoneNumber number) {
                    debugPrint('On Saved: $number');
                  },
                ),

                Obx(() {
                  return GestureDetector(
                    onTap: () {
                      DatePicker.showDatePicker(
                        context,
                        showTitleActions: true,
                        minTime: DateTime(1950, 1, 1),
                        maxTime: DateTime.now()
                            .subtract(const Duration(days: 365 * 10)),
                        onConfirm: (date) {
                          debugPrint('confirm $date');
                          controller.dateOfBirthController.value.text =
                              DateFormat('dd MMMM yyyy').format(date);
                          controller.birthDate = date;
                        },
                        currentTime: controller.birthDate,
                      );
                    },
                    child: AbsorbPointer(
                      child: TextFormField(
                        controller: controller.dateOfBirthController.value,
                        keyboardType: TextInputType.text,
                        //enabled: false,
                        readOnly: true,
                        decoration: const InputDecoration(
                          labelText: "Date of Birth",
                          hintText: "",
                        ),
                        validator: (value) {
                          return null;

                          // if (value!.isEmpty) {
                          //   return 'Required';
                          // }
                        },
                      ),
                    ),
                  );
                }),

                // term
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Row(children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: Obx(() => Checkbox(
                            checkColor: Colors.white,
                            activeColor: AppColor.secondaryColor,
                            shape: const CircleBorder(),
                            value: controller.isChecked.value,
                            onChanged: (value) {
                              controller.isChecked.value =
                                  !controller.isChecked.value;
                            },
                          )),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Flexible(
                      child: Column(children: [
                        RichText(
                          maxLines: 2,
                          text: TextSpan(children: [
                            const TextSpan(
                                text:
                                    "I have read and understood Automoment's ",
                                style: TextStyle(color: Colors.black)),
                            TextSpan(
                                text: "Data Protection Terms",
                                style: const TextStyle(
                                    color: AppColor.secondaryColor,
                                    fontWeight: FontWeight.bold),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () async {
                                    debugPrint("okk");
                                    Uri url = Uri.parse(
                                        "https://automoment.com.sg/terms-of-service/");
                                    launchInBrowser(url);
                                  }),
                          ]),
                        ),
                      ]),
                    ),
                  ]),
                ),
                const SizedBox(
                  height: 30,
                ),
                ElevatedButton(
                    style: ButtonStyle(
                        foregroundColor:
                            WidgetStateProperty.all<Color>(Colors.white),
                        backgroundColor: WidgetStateProperty.all<Color>(
                            AppColor.primaryButtonColor),
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side: const BorderSide(
                                    color: AppColor.primaryButtonColor)))),
                    onPressed: () async {
                      if (controller.formKey.value.currentState!.validate()) {
                        debugPrint("ok");

                        if (controller.passwordController.text !=
                            controller.passwordConfirmController.text) {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return const KMessageDialogView(
                                    content:
                                        "Incorrect password confirmation.");
                              });
                        } else if (!controller.isChecked.value) {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return const KMessageDialogView(
                                    content:
                                        "You need to read and understand Automoment's Data Protection Terms.");
                              });
                        } else {
                          if (await controller.register()) {
                            var param = {
                              "email": controller.emailController.text,
                              "deviceName": controller.deviceName,
                            };
                            Get.toNamed(Routes.VERIFY_EMAIL, arguments: param);
                          } else {
                            showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return KMessageDialogView(
                                      content: controller.message);
                                });
                            debugPrint("Register failed ${controller.message}");
                          }
                        }
                      } else {
                        debugPrint("not ok");
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: const Center(
                        child: Text("Sign Up", style: TextStyle(fontSize: 16)),
                      ),
                    )),

                const SizedBox(
                  height: 10,
                ),
                ElevatedButton(
                    style: ButtonStyle(
                        foregroundColor:
                            WidgetStateProperty.all<Color>(Colors.white),
                        backgroundColor: WidgetStateProperty.all<Color>(
                            AppColor.primaryButtonColor),
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side: const BorderSide(
                                    color: AppColor.primaryButtonColor)))),
                    onPressed: () async {
                      if (await controller.signInWithGoogle()) {
                        if (controller.isAllRequiredUserFieldsFilled()) {
                          Get.offAllNamed(Routes.BOTTOMBAR);
                        } else {
                          Get.offAllNamed(Routes.SOCIAL_LOGIN_FORM,
                              arguments: "Google");
                        }
                      } else {
                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return KMessageDialogView(
                                  content: controller.message);
                            });
                        debugPrint("login failed ${controller.message}");
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: Row(
                        children: [
                          const Spacer(),
                          Image.asset(
                            "assets/images/google.png",
                            height: 20,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          const Text("Sign Up with Google",
                              style: TextStyle(fontSize: 16)),
                          const Spacer(),
                        ],
                      ),
                    )),
                const SizedBox(
                  height: 10,
                ),
                (Platform.isIOS)
                    ? ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0),
                                    side: const BorderSide(
                                        color: AppColor.primaryButtonColor)))),
                        onPressed: () async {
                          //controller.signInWithGoogle();
                          if (await controller.signInWithApple()) {
                            if (controller.isAllRequiredUserFieldsFilled()) {
                              Get.offAllNamed(Routes.BOTTOMBAR);
                            } else {
                              Get.offAllNamed(Routes.SOCIAL_LOGIN_FORM,
                                  arguments: "Apple");
                            }
                          } else {
                            showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return KMessageDialogView(
                                      content: controller.message);
                                });
                            debugPrint("login failed ${controller.message}");
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: Row(
                            children: [
                              const Spacer(),
                              Image.asset(
                                "assets/images/apple.png",
                                height: 20,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Text("Sign Up with Apple",
                                  style: TextStyle(fontSize: 16)),
                              const Spacer(),
                            ],
                          ),
                        ))
                    : Container(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> launchInBrowser(Uri url) async {
    if (!await launchUrl(
      url,
      mode: LaunchMode.externalApplication,
    )) {
      throw 'Could not launch $url';
    }
  }
}
