import 'package:flutter/material.dart';

class CustomPopup extends StatelessWidget {
  final Widget child;
  final Function? onTap;

  const CustomPopup({super.key, required this.child, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Dimmed background
          GestureDetector(
            onTap: () {
              if (onTap != null) {
                onTap!();
              }
            },
            child: Container(
              color: Colors.black.withAlpha(26 * 5),
            ),
          ),
          // Centered popup content
          Center(
            child: Container(
              margin: const EdgeInsets.all(32),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: child,
            ),
          ),
        ],
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  bool _showPopup = false;

  void _togglePopup() {
    setState(() {
      _showPopup = !_showPopup;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Custom Popup Example')),
      body: Stack(
        children: [
          // Your main content
          Center(
            child: ElevatedButton(
              onPressed: _togglePopup,
              child: const Text('Show Popup'),
            ),
          ),
          // Custom popup
          if (_showPopup)
            CustomPopup(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('This is a custom popup'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _togglePopup,
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
