import 'dart:io';

import 'package:automoment/app/helpers/string_util.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';

import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../constants/app_config.dart';
import '../../shared/support_button.dart';
import '../controllers/gallery_detail_controller.dart';

class GalleryDetailView extends StatelessWidget {
  GalleryDetailView({super.key});

  final GalleryDetailController controller = Get.put(GalleryDetailController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.photos[controller.index].eventName),
        centerTitle: true,
        elevation: 1,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        actions: [
          GestureDetector(
            onTap: () async {
              if (await controller.fetchSharablePhotoUrl()) {
                showModalBottomSheet(
                    isScrollControlled: true,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    context: context,
                    builder: (BuildContext context) {
                      return SingleChildScrollView(
                        child: Column(
                          children: [
                            const SizedBox(
                              height: 20,
                            ),
                            const Text(
                              "Share Photo",
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                            GestureDetector(
                              onTap: () async {
                                // download photo
                                await controller.downloadSharablePhotoUrl(
                                    controller.photoUrl, context);
                              },
                              child: Column(
                                children: [
                                  Image.network(
                                    controller.photoThumbnailUrl,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  const Text(
                                    "Without Timing",
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                            GestureDetector(
                              onTap: () async {
                                // download photo
                                await controller.downloadSharablePhotoUrl(
                                    controller.photoWithBestTimeUrl, context);
                              },
                              child: Column(
                                children: [
                                  Image.network(
                                    controller.photoWithBestTimeThumbnailUrl,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  const Text(
                                    "With Best Time at Event",
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            GestureDetector(
                              onTap: () async {
                                // download photo
                                await controller.downloadSharablePhotoUrl(
                                    controller.photoWithPersonalBestUrl,
                                    context);
                              },
                              child: Column(
                                children: [
                                  Image.network(
                                    controller
                                        .photoWithPersonalBestThumbnailUrl,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  const Text(
                                    "With Personal Best",
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                          ],
                        ),
                      );
                    });
              }
            },
            child: const Padding(
                padding: EdgeInsets.only(right: 20),
                child: Icon(
                  Icons.share,
                  color: Colors.black,
                )),
          ),
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.black,
          ),
          child: Column(
            children: [
              const Spacer(),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Swiper(
                    itemBuilder: (BuildContext context, int index) {
                      return Image.network(
                        "${AppConfig.storageUrl}${controller.photos[index].photo}",
                        fit: BoxFit.contain,
                      );
                    },
                    itemCount: controller.photos.length,
                    pagination: const SwiperPagination(
                      builder: DotSwiperPaginationBuilder(
                          color: Colors.grey,
                          activeColor: Colors.white,
                          space: 5,
                          size: 5,
                          activeSize: 5),
                    ),
                    control: const SwiperControl(
                      color: Colors.white,
                    ),
                    index: controller.index,
                    onTap: (index) {},
                    onIndexChanged: (index) {
                      controller.index = index;
                      controller.caption.value =
                          controller.photos[index].caption;
                    }),
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Text(
                  controller.caption.value,
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white),
                ),
              ),
              const Spacer(),
            ],
          ),
        );
      }),
    );
  }

  Future<void> sharePhoto(BuildContext context) async {
    //
    final box = context.findRenderObject() as RenderBox?;
    double w = box?.size.width ?? 820;

    EasyLoading.show(status: 'Loading...');

    // controller.downloadSharablePhotoUrl

    String downloadUrl =
        "${AppConfig.storageUrl}${controller.photos[controller.index].photo}";
    String filename = StringUtil.getFilenameFromUrl(downloadUrl);

    final task = DownloadTask(
      url: downloadUrl,
      urlQueryParameters: {'q': 'pizza'},
      filename: filename,
      //headers: {'myHeader': 'value'},
      //directory: '',
      updates: Updates.statusAndProgress, // request status and progress updates
      //requiresWiFi: true,
      retries: 5,
      allowPause: false,
      //metaData: 'data for me'
    );

    final TaskStatusUpdate result = await FileDownloader().download(task,
        onProgress: (progress) => debugPrint('Progress: ${progress * 100}%'),
        onStatus: (status) => debugPrint('Status: $status'));

    EasyLoading.dismiss();

    switch (result.status) {
      case TaskStatus.complete:
        final Directory appDocumentsDir =
            await getApplicationDocumentsDirectory();

        if (Platform.isAndroid) {
          Share.shareXFiles([XFile("${appDocumentsDir.path}/$filename")],
              text: controller.photos[controller.index].caption,
              subject: controller.photos[controller.index].caption,
              sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
        } else if (Platform.isIOS) {
          Share.shareXFiles([XFile("${appDocumentsDir.path}/$filename")],
              sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
        }

        break;

      case TaskStatus.canceled:
        debugPrint('Download was canceled');
        break;

      case TaskStatus.paused:
        debugPrint('Download was paused');
        break;

      default:
        debugPrint('Download not successful');
    }
  }
}
